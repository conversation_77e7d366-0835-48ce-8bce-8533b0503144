import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { Workspace, IWorkspace } from '../models/Workspace';

interface SphereEngineConfig {
  customerId: string;
  accessToken: string;
  apiEndpoint: string;
}

interface CreateWorkspaceRequest {
  projectId?: string;
  projectType?: string;
  interviewUuid: string;
  files?: Array<{
    path: string;
    content: string;
  }>;
}

interface WorkspaceResponse {
  id: string;
  project: {
    id: string;
    name: string;
  };
  state: {
    code: number;
    name: string;
    error_reason?: string | null;
  };
  workspace_url: string;
  workspace_token?: string;
  created_at: string;
  terminated_at?: string | null;
  last_usage?: string | null;
  inactivity_timeout: number;
  workspace_token_required: boolean;
  workspace_init_failed: boolean;
  auto_resume?: boolean;
  retention_time_stopped?: number;
  retention_time_total?: number;
  permissions?: {
    enter: boolean;
    remove: boolean;
    stop: boolean;
  };
}

interface UserWorkspace {
  id: string;
  projectId: string;
  userId: string;
  createdAt: Date;
  lastAccessedAt: Date;
  status: 'starting' | 'running' | 'stopped' | 'error';
  workspaceData?: WorkspaceResponse;
}

// In-memory storage for user workspaces (in production, use a database)
const userWorkspaces = new Map<string, UserWorkspace[]>();

// Helper functions for workspace management
const getUserWorkspaces = (userId: string): UserWorkspace[] => {
  return userWorkspaces.get(userId) || [];
};

const saveUserWorkspace = (workspace: UserWorkspace): void => {
  const userId = workspace.userId;
  const workspaces = getUserWorkspaces(userId);

  // Remove existing workspace with same project ID
  const filteredWorkspaces = workspaces.filter(w => w.projectId !== workspace.projectId);
  filteredWorkspaces.push(workspace);

  userWorkspaces.set(userId, filteredWorkspaces);
};

const findUserWorkspace = (userId: string, projectId: string): UserWorkspace | undefined => {
  const workspaces = getUserWorkspaces(userId);
  return workspaces.find(w => w.projectId === projectId);
};

const removeUserWorkspace = (userId: string, workspaceId: string): void => {
  const workspaces = getUserWorkspaces(userId);
  const filteredWorkspaces = workspaces.filter(w => w.id !== workspaceId);
  userWorkspaces.set(userId, filteredWorkspaces);
};

const updateWorkspaceStatus = (userId: string, workspaceId: string, status: UserWorkspace['status']): void => {
  const workspaces = getUserWorkspaces(userId);
  const workspace = workspaces.find(w => w.id === workspaceId);
  if (workspace) {
    workspace.status = status;
    workspace.lastAccessedAt = new Date();
  }
};

// Get Sphere Engine configuration from environment
export const getSphereEngineConfig = (): SphereEngineConfig => {
  return {
    customerId: process.env.SPHERE_ENGINE_CUSTOMER_ID || '',
    accessToken: process.env.SPHERE_ENGINE_ACCESS_TOKEN || '',
    apiEndpoint: process.env.SPHERE_ENGINE_API_ENDPOINT || ''
  };
};

// Helper function to check workspace state on Sphere Engine
export const checkWorkspaceState = async (workspaceId: string, config: SphereEngineConfig): Promise<{ isRunning: boolean; stateCode?: number; stateName?: string }> => {
  try {
    if (!workspaceId || !config.accessToken || !config.apiEndpoint) {
      console.error('Invalid parameters for workspace state check:', { workspaceId: !!workspaceId, hasToken: !!config.accessToken, hasEndpoint: !!config.apiEndpoint });
      return { isRunning: false };
    }

    const apiUrl = `https://${config.apiEndpoint}/api/v1/workspaces/${workspaceId}?access_token=${config.accessToken}`;
    console.log(`🔍 Checking workspace ${workspaceId} state on Sphere Engine...`);
    console.log(`🌐 API URL: ${apiUrl}`);

    const response = await fetch(apiUrl, {
      method: 'GET'
    });

    console.log(`📡 Sphere Engine API response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ API Error Response Body:`, errorText);
    }

    if (response.ok) {
      const data = await response.json() as any; // Use any to see the actual structure
      console.log(`📡 RAW Sphere Engine API Response:`, JSON.stringify(data, null, 2));

      // Debug the actual structure
      console.log(`🔍 RESPONSE STRUCTURE DEBUG:`);
      console.log(`   - data exists: ${!!data}`);
      console.log(`   - data.workspace exists: ${!!data.workspace}`);
      console.log(`   - data.state exists: ${!!data.state}`);
      if (data.workspace) {
        console.log(`   - data.workspace.state exists: ${!!data.workspace.state}`);
      }
      if (data.state) {
        console.log(`   - data.state.code: ${data.state.code}`);
        console.log(`   - data.state.name: ${data.state.name}`);
      }

      // Try to find the state in different possible locations
      let stateCode: number | undefined;
      let stateName: string | undefined;

      if (data.workspace && data.workspace.state) {
        // Case 1: state is nested under workspace
        stateCode = data.workspace.state.code;
        stateName = data.workspace.state.name;
        console.log(`📍 Found state in data.workspace.state`);
      } else if (data.state) {
        // Case 2: state is directly in the response
        stateCode = data.state.code;
        stateName = data.state.name;
        console.log(`📍 Found state in data.state`);
      } else {
        console.log(`❌ Could not find state in response structure`);
        return { isRunning: false };
      }

      if (stateCode !== undefined && stateName !== undefined) {
        console.log(`🔍 PARSING STATE:`);
        console.log(`   - Raw state code: ${stateCode} (type: ${typeof stateCode})`);
        console.log(`   - Raw state name: "${stateName}" (type: ${typeof stateName})`);

        // Based on your documentation:
        // 1 = starting, 2 = running, 3 = stopped, 4 = removed, 5 = error
        // We consider workspace "running" if it's starting (1) or running (2)
        const isRunning = stateCode === 1 || stateCode === 2;

        console.log(`🎯 STATE EVALUATION:`);
        console.log(`   - stateCode === 1 (starting): ${stateCode === 1}`);
        console.log(`   - stateCode === 2 (running): ${stateCode === 2}`);
        console.log(`   - Final isRunning result: ${isRunning}`);

        console.log(`✅ Workspace ${workspaceId} state: code=${stateCode}, name=${stateName}, isRunning=${isRunning}`);
        return { isRunning, stateCode, stateName };
      } else {
        console.log(`❌ State code or name is undefined`);
        return { isRunning: false };
      }
    } else {
      console.warn(`❌ Workspace state check failed with status ${response.status}:`, response.statusText);
      console.warn(`   This means the workspace might not exist on Sphere Engine or there's an API issue`);
      return { isRunning: false };
    }
  } catch (error) {
    console.error('❌ ERROR checking workspace state on Sphere Engine:', error);
    console.error('   This could be a network issue, invalid URL, or API problem');
    return { isRunning: false };
  }
};

// Helper function to find existing workspace for user + interview combination
export const findExistingWorkspace = async (userId: string, interviewUuid: string): Promise<IWorkspace | null> => {
  try {
    if (!userId || !interviewUuid) {
      console.error('Invalid parameters for finding workspace:', { userId: !!userId, interviewUuid: !!interviewUuid });
      return null;
    }

    console.log(`🔍 Database query: findOne({ userId: "${userId}", interviewUuid: "${interviewUuid}" })`);

    const workspace = await Workspace.findOne({
      userId,
      interviewUuid
    });

    console.log(`📊 Database query result:`, workspace ? `Found workspace ${workspace.sphereEngineWorkspaceId}` : 'No workspace found');

    if (workspace) {
      console.log(`✅ Found existing workspace for user ${userId}, interview ${interviewUuid}:`, workspace.sphereEngineWorkspaceId);
      console.log(`   Created: ${workspace.createdAt}, Updated: ${workspace.updatedAt}`);
    } else {
      console.log(`❌ No existing workspace found for user ${userId}, interview ${interviewUuid}`);
    }

    return workspace;
  } catch (error) {
    console.error('Database error finding existing workspace:', error);
    return null;
  }
};

// Helper function to create workspace record in database
const createWorkspaceRecord = async (
  userId: string,
  interviewUuid: string,
  sphereEngineWorkspaceId: string,
  sphereEngineProjectId: string
): Promise<IWorkspace> => {
  try {
    if (!userId || !interviewUuid || !sphereEngineWorkspaceId || !sphereEngineProjectId) {
      throw new Error('Missing required parameters for workspace record creation');
    }

    const workspace = new Workspace({
      userId,
      interviewUuid,
      sphereEngineWorkspaceId,
      sphereEngineProjectId
    });

    const savedWorkspace = await workspace.save();
    console.log(`Created workspace record for user ${userId}, interview ${interviewUuid}:`, savedWorkspace.sphereEngineWorkspaceId);
    return savedWorkspace;
  } catch (error) {
    console.error('Error creating workspace record:', error);
    throw error;
  }
};

// Get or create workspace for user + interview combination
export const getOrCreateWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid, projectId, files } = req.body;

    if (!req.user?.userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    if (!interviewUuid) {
      return res.status(400).json({
        success: false,
        message: 'Interview UUID is required'
      });
    }

    const config = getSphereEngineConfig();
    if (!config.accessToken || !config.apiEndpoint) {
      return res.status(500).json({
        success: false,
        message: 'Sphere Engine configuration missing'
      });
    }

    // Check if workspace already exists for this user + interview combination
    console.log(`🔍 Checking for existing workspace - User: ${req.user.userId}, Interview: ${interviewUuid}`);
    const existingWorkspace = await findExistingWorkspace(req.user.userId, interviewUuid);

    if (existingWorkspace) {
      console.log(`✅ Found existing workspace: ${existingWorkspace.sphereEngineWorkspaceId}`);

      // Check the workspace state on Sphere Engine
      console.log(`🔍 Checking workspace state on Sphere Engine for workspace ID: ${existingWorkspace.sphereEngineWorkspaceId}`);
      const stateCheck = await checkWorkspaceState(existingWorkspace.sphereEngineWorkspaceId, config);
      console.log(`📊 DETAILED State check result:`, JSON.stringify(stateCheck, null, 2));
      console.log(`📊 State breakdown - isRunning: ${stateCheck.isRunning}, stateCode: ${stateCheck.stateCode}, stateName: ${stateCheck.stateName}`);

      if (stateCheck.isRunning) {
        console.log(`✅ WORKSPACE IS RUNNING - returning existing workspace`);
        console.log(`   - Workspace ID: ${existingWorkspace.sphereEngineWorkspaceId}`);
        console.log(`   - State Code: ${stateCheck.stateCode}`);
        console.log(`   - State Name: ${stateCheck.stateName}`);

        // Update the record timestamp and return existing workspace
        await existingWorkspace.save();

        return res.json({
          success: true,
          data: {
            workspaceId: existingWorkspace.sphereEngineWorkspaceId,
            projectId: existingWorkspace.sphereEngineProjectId,
            isExisting: true
          },
          message: `Using existing workspace (state: ${stateCheck.stateName})`
        });
      } else {
        console.log(`❌ WORKSPACE IS NOT RUNNING - will create new one`);
        console.log(`   - Workspace ID: ${existingWorkspace.sphereEngineWorkspaceId}`);
        console.log(`   - State Code: ${stateCheck.stateCode}`);
        console.log(`   - State Name: ${stateCheck.stateName}`);
        console.log(`   - Reason: State code ${stateCheck.stateCode} is not 1 (starting) or 2 (running)`);

        // Delete the non-running workspace record
        console.log(`🗑️ Deleting non-running workspace record from database`);
        await Workspace.deleteOne({ _id: existingWorkspace._id });
      }
    } else {
      console.log(`❌ No existing workspace found for user ${req.user.userId}, interview ${interviewUuid}`);
    }

    // Create new workspace - delegate to existing createWorkspace logic
    console.log(`🆕 CREATING NEW WORKSPACE:`);
    console.log(`   - User ID: ${req.user.userId}`);
    console.log(`   - Interview UUID: ${interviewUuid}`);
    console.log(`   - Project ID: ${projectId}`);
    console.log(`   - Files count: ${files ? files.length : 0}`);

    req.body = { projectId, interviewUuid, files };
    return createWorkspace(req, res);

  } catch (error: any) {
    console.error('Error getting or creating workspace:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get or create workspace',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Create a new Sphere Engine workspace
export const createWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { projectId, interviewUuid, files }: CreateWorkspaceRequest = req.body;
    const config = getSphereEngineConfig();

    if (!req.user?.userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    if (!interviewUuid) {
      return res.status(400).json({
        success: false,
        message: 'Interview UUID is required'
      });
    }

    console.log('Sphere Engine config check:', {
      hasAccessToken: !!config.accessToken,
      hasApiEndpoint: !!config.apiEndpoint,
      customerId: config.customerId,
      apiEndpoint: config.apiEndpoint
    });

    if (!config.accessToken || !config.apiEndpoint) {
      return res.status(500).json({
        success: false,
        message: 'Sphere Engine configuration missing'
      });
    }

    // Check if workspace already exists for this user + interview combination
    const existingWorkspace = await findExistingWorkspace(req.user.userId, interviewUuid);

    if (existingWorkspace) {
      // Check the workspace state on Sphere Engine
      const stateCheck = await checkWorkspaceState(existingWorkspace.sphereEngineWorkspaceId, config);

      if (stateCheck.isRunning) {
        // Update the record timestamp and return existing workspace
        await existingWorkspace.save();

        return res.json({
          success: true,
          data: {
            workspaceId: existingWorkspace.sphereEngineWorkspaceId,
            projectId: existingWorkspace.sphereEngineProjectId
          },
          message: `Using existing workspace (state: ${stateCheck.stateName})`,
          isExisting: true
        });
      } else {
        // Delete the non-running workspace record
        await Workspace.deleteOne({ _id: existingWorkspace._id });
      }
    }

    // Create new workspace via Sphere Engine API
    const formData = new URLSearchParams();

    // Try common default project IDs or the one provided
    const defaultProjectIds = [
      projectId,
      'default',
      'demo',
      'example',
      'web-app',
      'nodejs',
      'python'
    ].filter(Boolean);

    // Use the first available project ID
    const useProjectId = defaultProjectIds[0] || 'default';
    formData.append('project_id', useProjectId);

    console.log('Creating new workspace for user:', req.user.userId, 'interview:', interviewUuid);
    console.log('Trying project_id:', useProjectId);

    const apiUrl = `https://${config.apiEndpoint}/api/v1/workspaces?access_token=${config.accessToken}`;
    console.log('Making request to:', apiUrl);

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Sphere Engine API error:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        body: errorText
      });
      return res.status(response.status).json({
        success: false,
        message: `Failed to create workspace: ${response.status} ${response.statusText}`,
        error: errorText
      });
    }

    const data = await response.json() as { workspace: WorkspaceResponse };

    // If files are provided, upload them to the workspace
    if (files && files.length > 0 && data.workspace?.id) {
      await uploadFilesToWorkspace(data.workspace.id, files, config);
    }

    // Save workspace to database with user + interview scoping
    if (data.workspace?.id) {
      try {
        await createWorkspaceRecord(
          req.user.userId,
          interviewUuid,
          data.workspace.id,
          useProjectId
        );
      } catch (error) {
        console.error('Error saving workspace to database:', error);
        // Continue anyway since workspace was created successfully
      }
    }

    res.json({
      success: true,
      data: data.workspace,
      message: 'Workspace created successfully',
      isExisting: false
    });

  } catch (error: any) {
    console.error('Error creating workspace:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create workspace',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// List user workspaces
export const listUserWorkspaces = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const workspaces = getUserWorkspaces(req.user.userId);

    // Filter out workspaces older than 24 hours
    const now = new Date();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    const validWorkspaces = workspaces.filter(workspace => {
      const age = now.getTime() - workspace.createdAt.getTime();
      return age < maxAge;
    });

    // Update storage if we filtered out any workspaces
    if (validWorkspaces.length !== workspaces.length) {
      userWorkspaces.set(req.user.userId, validWorkspaces);
    }

    res.json({
      success: true,
      data: validWorkspaces,
      message: 'User workspaces retrieved successfully'
    });

  } catch (err: any) {
    console.error('Error listing user workspaces:', err);
    res.status(500).json({
      success: false,
      message: 'Failed to list workspaces',
      error: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
};

// Check for existing workspace by interview UUID using database and Sphere Engine API
export const checkExistingWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid } = req.params;

    if (!req.user?.userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    if (!interviewUuid) {
      return res.status(400).json({
        success: false,
        message: 'Interview UUID is required'
      });
    }

    const config = getSphereEngineConfig();

    if (!config.accessToken || !config.apiEndpoint) {
      return res.status(500).json({
        success: false,
        message: 'Sphere Engine configuration missing'
      });
    }

    try {
      // Check if workspace exists in database for this user + interview combination
      const existingWorkspace = await findExistingWorkspace(req.user.userId, interviewUuid);

      if (!existingWorkspace) {
        return res.json({
          success: false,
          message: 'No workspace found for this interview session'
        });
      }

      // Check the workspace state on Sphere Engine
      const stateCheck = await checkWorkspaceState(existingWorkspace.sphereEngineWorkspaceId, config);

      if (!stateCheck.isRunning) {
        // Delete the non-running workspace record
        await Workspace.deleteOne({ _id: existingWorkspace._id });

        return res.json({
          success: false,
          message: `Workspace is not running (state: ${stateCheck.stateName})`
        });
      }

      // Update the record timestamp
      await existingWorkspace.save();

      res.json({
        success: true,
        data: {
          workspaceId: existingWorkspace.sphereEngineWorkspaceId,
          projectId: existingWorkspace.sphereEngineProjectId,
          createdAt: existingWorkspace.createdAt,
          updatedAt: existingWorkspace.updatedAt
        },
        message: 'Found existing workspace for this interview session'
      });

    } catch (error) {
      console.error('Error checking existing workspace:', error);
      res.json({
        success: false,
        message: 'Could not check existing workspace'
      });
    }

  } catch (err: any) {
    console.error('Error checking existing workspace:', err);
    res.status(500).json({
      success: false,
      message: 'Failed to check existing workspace',
      error: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
};

// Get workspace status
export const getWorkspaceStatus = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceId } = req.params;
    const config = getSphereEngineConfig();

    if (!config.accessToken || !config.apiEndpoint) {
      return res.status(500).json({
        success: false,
        message: 'Sphere Engine configuration missing'
      });
    }

    const response = await fetch(`https://${config.apiEndpoint}/api/v1/workspaces/${workspaceId}?access_token=${config.accessToken}`);

    if (!response.ok) {
      return res.status(response.status).json({
        success: false,
        message: `Failed to get workspace status: ${response.statusText}`
      });
    }

    const data = await response.json() as { workspace: WorkspaceResponse };

    res.json({
      success: true,
      data: data.workspace,
      message: 'Workspace status retrieved successfully'
    });

  } catch (error: any) {
    console.error('Error getting workspace status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get workspace status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Stop a workspace
export const stopWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceId } = req.params;
    const config = getSphereEngineConfig();

    if (!config.accessToken || !config.apiEndpoint) {
      return res.status(500).json({
        success: false,
        message: 'Sphere Engine configuration missing'
      });
    }

    const response = await fetch(`https://${config.apiEndpoint}/api/v1/workspaces/${workspaceId}/stop?access_token=${config.accessToken}`, {
      method: 'POST'
    });

    if (!response.ok) {
      return res.status(response.status).json({
        success: false,
        message: `Failed to stop workspace: ${response.statusText}`
      });
    }

    const data = await response.json() as { workspace: WorkspaceResponse };

    res.json({
      success: true,
      data: data.workspace,
      message: 'Workspace stopped successfully'
    });

  } catch (error: any) {
    console.error('Error stopping workspace:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to stop workspace',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Remove a workspace
export const removeWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceId } = req.params;
    const config = getSphereEngineConfig();

    if (!config.accessToken || !config.apiEndpoint) {
      return res.status(500).json({
        success: false,
        message: 'Sphere Engine configuration missing'
      });
    }

    const response = await fetch(`https://${config.apiEndpoint}/api/v1/workspaces/${workspaceId}?access_token=${config.accessToken}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      return res.status(response.status).json({
        success: false,
        message: `Failed to remove workspace: ${response.statusText}`
      });
    }

    res.json({
      success: true,
      message: 'Workspace removed successfully'
    });

  } catch (error: any) {
    console.error('Error removing workspace:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove workspace',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Create a project for code execution
export const createProject = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { name, type, description, files } = req.body;
    const config = getSphereEngineConfig();

    if (!config.accessToken || !config.apiEndpoint) {
      return res.status(500).json({
        success: false,
        message: 'Sphere Engine configuration missing'
      });
    }

    // For now, return a mock project since project creation requires admin access
    // In production, you would need to create projects through the Sphere Engine panel
    const mockProject = {
      id: `project-${Date.now()}`,
      name: name || 'Generated Project',
      type: type || 'web',
      description: description || 'Auto-generated project for code execution'
    };

    res.json({
      success: true,
      data: mockProject,
      message: 'Project created successfully (mock)'
    });

  } catch (error: any) {
    console.error('Error creating project:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create project',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Helper function to upload files to workspace
const uploadFilesToWorkspace = async (
  workspaceId: string, 
  files: Array<{ path: string; content: string }>, 
  config: SphereEngineConfig
): Promise<void> => {
  try {
    for (const file of files) {
      const formData = new FormData();
      formData.append('path', file.path);
      formData.append('content', file.content);

      await fetch(`https://${config.apiEndpoint}/api/v1/workspaces/${workspaceId}/files?access_token=${config.accessToken}`, {
        method: 'POST',
        body: formData
      });
    }
  } catch (error) {
    console.error('Error uploading files to workspace:', error);
    // Don't throw error, just log it as workspace creation was successful
  }
};

// Upload file to workspace
export const uploadFileToWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceId } = req.params;
    const { filepath, content } = req.body;
    const config = getSphereEngineConfig();

    if (!config.accessToken || !config.apiEndpoint) {
      return res.status(500).json({
        success: false,
        message: 'Sphere Engine configuration missing'
      });
    }

    if (!filepath) {
      return res.status(400).json({
        success: false,
        message: 'File path is required'
      });
    }

    if (!content) {
      return res.status(400).json({
        success: false,
        message: 'File content is required'
      });
    }

    // Create FormData for file upload
    const formData = new FormData();

    // Create a blob from the content for file upload
    const blob = new Blob([content], { type: 'text/plain' });
    formData.append('filedata', blob, filepath.split('/').pop() || 'file.txt');
    formData.append('filepath', filepath);

    const response = await fetch(`https://${config.apiEndpoint}/api/v1/workspaces/${workspaceId}/files?access_token=${config.accessToken}`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Sphere Engine file upload error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      return res.status(response.status).json({
        success: false,
        message: `Failed to upload file: ${response.status} ${response.statusText}`,
        error: errorText
      });
    }

    const data = await response.json();

    res.json({
      success: true,
      data: data,
      message: 'File uploaded successfully'
    });

  } catch (error: any) {
    console.error('Error uploading file to workspace:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload file to workspace',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get file from workspace
export const getFileFromWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceId, filepath } = req.params;
    const config = getSphereEngineConfig();

    if (!config.accessToken || !config.apiEndpoint) {
      return res.status(500).json({
        success: false,
        message: 'Sphere Engine configuration missing'
      });
    }

    // Encode the filepath for URL
    const encodedFilepath = encodeURIComponent(filepath);

    const response = await fetch(`https://${config.apiEndpoint}/api/v1/workspaces/${workspaceId}/files/${encodedFilepath}?access_token=${config.accessToken}`);

    if (!response.ok) {
      return res.status(response.status).json({
        success: false,
        message: `Failed to get file: ${response.status} ${response.statusText}`
      });
    }

    // Get the file content as text
    const fileContent = await response.text();

    res.json({
      success: true,
      data: {
        filepath: filepath,
        content: fileContent
      },
      message: 'File retrieved successfully'
    });

  } catch (error: any) {
    console.error('Error getting file from workspace:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get file from workspace',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// List files in workspace
export const listWorkspaceFiles = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceId } = req.params;
    const config = getSphereEngineConfig();

    if (!config.accessToken || !config.apiEndpoint) {
      return res.status(500).json({
        success: false,
        message: 'Sphere Engine configuration missing'
      });
    }

    // Make request to Sphere Engine API to list files
    const response = await fetch(`https://${config.apiEndpoint}/api/v1/workspaces/${workspaceId}/files?access_token=${config.accessToken}`);

    if (!response.ok) {
      // If file listing is not supported, return a helpful message with mock data
      if (response.status === 405) {
        const mockFiles = [
          {
            path: 'HELLO.md',
            type: 'file',
            size: 45,
            lastModified: new Date().toISOString()
          },
          {
            path: 'README.md',
            type: 'file',
            size: 234,
            lastModified: new Date().toISOString()
          }
        ];

        return res.json({
          success: true,
          files: mockFiles,
          message: 'File listing not supported by workspace. Showing sample files. Use file upload to add files.'
        });
      }

      return res.status(response.status).json({
        success: false,
        message: `Failed to list files: ${response.status} ${response.statusText}`
      });
    }

    const data = await response.json() as any;

    res.json({
      success: true,
      files: data.files || data || [],
      message: 'Files listed successfully'
    });

  } catch (error: any) {
    console.error('Error listing workspace files:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to list workspace files',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Modify existing file in workspace (alias for upload with overwrite)
export const modifyFileInWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceId } = req.params;
    const { filepath, content } = req.body;
    const config = getSphereEngineConfig();

    if (!config.accessToken || !config.apiEndpoint) {
      return res.status(500).json({
        success: false,
        message: 'Sphere Engine configuration missing'
      });
    }

    if (!filepath) {
      return res.status(400).json({
        success: false,
        message: 'File path is required'
      });
    }

    if (content === undefined || content === null) {
      return res.status(400).json({
        success: false,
        message: 'File content is required'
      });
    }

    // Create FormData for file upload (this will overwrite existing file)
    const formData = new FormData();

    // Create a blob from the content for file upload
    const blob = new Blob([content], { type: 'text/plain' });
    formData.append('filedata', blob, filepath.split('/').pop() || 'file.txt');
    formData.append('filepath', filepath);

    const response = await fetch(`https://${config.apiEndpoint}/api/v1/workspaces/${workspaceId}/files?access_token=${config.accessToken}`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Sphere Engine file modification error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      return res.status(response.status).json({
        success: false,
        message: `Failed to modify file: ${response.status} ${response.statusText}`,
        error: errorText
      });
    }

    const data = await response.json();

    res.json({
      success: true,
      data: data,
      message: 'File modified successfully'
    });

  } catch (error: any) {
    console.error('Error modifying file in workspace:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to modify file in workspace',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get available programming languages from Sphere Engine
export const getSupportedLanguages = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // Static list of commonly supported languages
    // In production, you might want to fetch this from Sphere Engine API
    const languages = [
      { id: 1, name: 'C++', extension: 'cpp' },
      { id: 10, name: 'Java', extension: 'java' },
      { id: 11, name: 'C', extension: 'c' },
      { id: 116, name: 'Python 3.x', extension: 'py' },
      { id: 56, name: 'Node.js', extension: 'js' },
      { id: 57, name: 'TypeScript', extension: 'ts' },
      { id: 114, name: 'Go', extension: 'go' },
      { id: 93, name: 'Rust', extension: 'rs' },
      { id: 39, name: 'Scala', extension: 'scala' },
      { id: 21, name: 'Haskell', extension: 'hs' },
      { id: 29, name: 'PHP', extension: 'php' },
      { id: 17, name: 'Ruby', extension: 'rb' }
    ];

    res.json({
      success: true,
      data: languages,
      message: 'Supported languages retrieved successfully'
    });

  } catch (error: any) {
    console.error('Error getting supported languages:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get supported languages',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get available projects from Sphere Engine
export const getAvailableProjects = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const config = getSphereEngineConfig();

    if (!config.accessToken || !config.apiEndpoint) {
      return res.status(500).json({
        success: false,
        message: 'Sphere Engine configuration missing'
      });
    }

    // Try to get projects list (this endpoint might not be available in all Sphere Engine accounts)
    const response = await fetch(`https://${config.apiEndpoint}/api/v1/projects?access_token=${config.accessToken}`);
    
    if (response.ok) {
      const data = await response.json();
      res.json({
        success: true,
        data: data,
        message: 'Available projects retrieved successfully'
      });
    } else {
      // If projects endpoint is not available, return helpful information
      res.json({
        success: false,
        message: 'Cannot retrieve projects list. Please check your Sphere Engine panel for available project IDs.',
        suggestion: 'Common project IDs to try: "default", "demo", "example", "web-app", "nodejs", "python"'
      });
    }

  } catch (error: any) {
    console.error('Error getting available projects:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get available projects',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
      suggestion: 'Please create a project in your Sphere Engine panel and use its ID'
    });
  }
}; 