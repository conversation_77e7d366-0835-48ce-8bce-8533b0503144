import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import fs from 'fs/promises';
import path from 'path';

interface WorkspaceFile {
  path: string;
  content: string;
  language: string;
  isDirectory: boolean;
  lastModified: Date;
}

interface WorkspaceStructure {
  [key: string]: any;
}

// Base workspace directory - you can make this configurable
export const BASE_WORKSPACE_DIR = '/Users/<USER>/Desktop/Mergen-AI/mergen-code/workspaces';

export class WorkspaceManager {
  static async createWorkspaceDir(workspaceName: string): Promise<string> {
    const workspaceDir = path.join(BASE_WORKSPACE_DIR, workspaceName);
    await fs.mkdir(workspaceDir, { recursive: true });
    return workspaceDir;
  }

  static async ensureWorkspaceExists(workspacePath: string): Promise<boolean> {
    try {
      const stats = await fs.stat(workspacePath);
      return stats.isDirectory();
    } catch {
      return false;
    }
  }

  static async readWorkspaceStructure(workspacePath: string, basePath: string = ''): Promise<WorkspaceStructure> {
    const structure: WorkspaceStructure = {};

    try {
      const items = await fs.readdir(workspacePath, { withFileTypes: true });

      for (const item of items) {
        // Skip hidden files and node_modules
        if (item.name.startsWith('.') || item.name === 'node_modules') {
          continue;
        }

        const fullPath = path.join(workspacePath, item.name);
        const relativePath = basePath ? `${basePath}/${item.name}` : item.name;

        if (item.isDirectory()) {
          const children = await this.readWorkspaceStructure(fullPath, relativePath);
          structure[item.name] = {
            type: 'folder',
            children: children
          };
        } else {
          try {
            const content = await fs.readFile(fullPath, 'utf-8');
            const stats = await fs.stat(fullPath);
            
            structure[item.name] = {
              type: 'file',
              content: content,
              language: this.getLanguageFromFilename(item.name),
              lastModified: stats.mtime
            };
          } catch (error) {
            console.warn(`Could not read file ${fullPath}:`, error);
            // Still include the file but with empty content
            structure[item.name] = {
              type: 'file',
              content: '',
              language: this.getLanguageFromFilename(item.name),
              lastModified: new Date()
            };
          }
        }
      }
    } catch (error) {
      console.error(`Error reading workspace ${workspacePath}:`, error);
    }

    return structure;
  }

  static async writeFile(workspacePath: string, filePath: string, content: string): Promise<void> {
    const fullPath = path.join(workspacePath, filePath);
    const dirPath = path.dirname(fullPath);
    
    // Ensure directory exists
    await fs.mkdir(dirPath, { recursive: true });
    
    // Write file
    await fs.writeFile(fullPath, content, 'utf-8');
  }

  static async deleteFile(workspacePath: string, filePath: string): Promise<void> {
    const fullPath = path.join(workspacePath, filePath);
    await fs.unlink(fullPath);
  }

  static async readFile(workspacePath: string, filePath: string): Promise<string> {
    const fullPath = path.join(workspacePath, filePath);
    return await fs.readFile(fullPath, 'utf-8');
  }

  static getLanguageFromFilename(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    const langMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'json': 'json',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'yml': 'yaml',
      'yaml': 'yaml',
      'xml': 'xml',
      'md': 'markdown',
      'txt': 'text',
      'env': 'bash',
      'sh': 'bash',
      'dockerfile': 'dockerfile'
    };
    return langMap[ext || ''] || 'text';
  }
}

// Get workspace structure for a specific workspace
export const getWorkspaceStructure = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const workspacePath = path.join(BASE_WORKSPACE_DIR, workspaceName);

    // Check if workspace exists
    const exists = await WorkspaceManager.ensureWorkspaceExists(workspacePath);
    if (!exists) {
      return res.status(404).json({
        success: false,
        message: 'Workspace not found'
      });
    }

    const structure = await WorkspaceManager.readWorkspaceStructure(workspacePath);

    res.json({
      success: true,
      data: {
        workspacePath,
        structure
      },
      message: 'Workspace structure retrieved successfully'
    });

  } catch (error: any) {
    console.error('Error getting workspace structure:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving workspace structure',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Read a specific file from workspace
export const readWorkspaceFile = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const { filePath } = req.body;
    
    const workspacePath = path.join(BASE_WORKSPACE_DIR, workspaceName);
    const content = await WorkspaceManager.readFile(workspacePath, filePath);

    res.json({
      success: true,
      data: {
        path: filePath,
        content,
        language: WorkspaceManager.getLanguageFromFilename(path.basename(filePath))
      },
      message: 'File read successfully'
    });

  } catch (error: any) {
    console.error('Error reading workspace file:', error);
    res.status(500).json({
      success: false,
      message: 'Error reading file',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Write a file to workspace
export const writeWorkspaceFile = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const { filePath, content } = req.body;
    
    const workspacePath = path.join(BASE_WORKSPACE_DIR, workspaceName);
    await WorkspaceManager.writeFile(workspacePath, filePath, content);

    res.json({
      success: true,
      data: {
        path: filePath,
        content,
        language: WorkspaceManager.getLanguageFromFilename(path.basename(filePath))
      },
      message: 'File saved successfully'
    });

  } catch (error: any) {
    console.error('Error writing workspace file:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving file',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Delete a file from workspace
export const deleteWorkspaceFile = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const { filePath } = req.body;
    
    const workspacePath = path.join(BASE_WORKSPACE_DIR, workspaceName);
    await WorkspaceManager.deleteFile(workspacePath, filePath);

    res.json({
      success: true,
      message: 'File deleted successfully'
    });

  } catch (error: any) {
    console.error('Error deleting workspace file:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting file',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Create a new workspace
export const createWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.body;
    
    const workspacePath = await WorkspaceManager.createWorkspaceDir(workspaceName);

    res.json({
      success: true,
      data: {
        workspaceName,
        workspacePath
      },
      message: 'Workspace created successfully'
    });

  } catch (error: any) {
    console.error('Error creating workspace:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating workspace',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// List all available workspaces
export const listWorkspaces = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const items = await fs.readdir(BASE_WORKSPACE_DIR, { withFileTypes: true });
    const workspaces = items
      .filter(item => item.isDirectory())
      .map(item => item.name);

    res.json({
      success: true,
      data: workspaces,
      message: 'Workspaces retrieved successfully'
    });

  } catch (error: any) {
    console.error('Error listing workspaces:', error);
    res.status(500).json({
      success: false,
      message: 'Error listing workspaces',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Save workspace to local filesystem
export const saveWorkspaceToFilesystem = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const { files } = req.body; // { [filePath: string]: string }

    if (!files || typeof files !== 'object') {
      return res.status(400).json({
        success: false,
        message: 'Files object is required'
      });
    }

    const workspacePath = path.join(BASE_WORKSPACE_DIR, workspaceName);

    // Ensure workspace directory exists
    await WorkspaceManager.createWorkspaceDir(workspaceName);

    // Save all files
    const savedFiles: string[] = [];
    for (const [filePath, content] of Object.entries(files)) {
      if (typeof content === 'string') {
        await WorkspaceManager.writeFile(workspacePath, filePath, content);
        savedFiles.push(filePath);
      }
    }

    res.json({
      success: true,
      data: {
        workspaceName,
        savedFiles,
        count: savedFiles.length
      },
      message: `Successfully saved ${savedFiles.length} files to filesystem`
    });

  } catch (error: any) {
    console.error('Error saving workspace to filesystem:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving workspace to filesystem',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Apply file changes using diffs (optimized payload)
export const applyWorkspaceChanges = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const { changes } = req.body; // Array of file changes with diffs

    if (!changes || !Array.isArray(changes)) {
      return res.status(400).json({
        success: false,
        message: 'Changes array is required'
      });
    }

    const workspacePath = path.join(BASE_WORKSPACE_DIR, workspaceName);

    // Ensure workspace directory exists
    await WorkspaceManager.createWorkspaceDir(workspaceName);

    const appliedChanges: string[] = [];
    const errors: { filePath: string; error: string }[] = [];

    for (const change of changes) {
      try {
        const { filePath, type, operations } = change;

        if (!filePath || !type) {
          errors.push({ filePath: filePath || 'unknown', error: 'Missing filePath or type' });
          continue;
        }

        switch (type) {
          case 'create':
            // Create new file
            await WorkspaceManager.writeFile(workspacePath, filePath, change.content || '');
            appliedChanges.push(filePath);
            break;

          case 'delete':
            // Delete file
            await WorkspaceManager.deleteFile(workspacePath, filePath);
            appliedChanges.push(filePath);
            break;

          case 'modify':
            // Apply diff operations to existing file
            if (!operations || !Array.isArray(operations)) {
              errors.push({ filePath, error: 'Missing operations for modify type' });
              continue;
            }

            // Read current file content
            let currentContent = '';
            try {
              currentContent = await WorkspaceManager.readFile(workspacePath, filePath);
            } catch (readError) {
              // File doesn't exist, treat as create
              currentContent = '';
            }

            // Apply operations in sequence
            const newContent = applyDiffOperations(currentContent, operations);
            await WorkspaceManager.writeFile(workspacePath, filePath, newContent);
            appliedChanges.push(filePath);
            break;

          default:
            errors.push({ filePath, error: `Unknown change type: ${type}` });
        }
      } catch (error: any) {
        errors.push({ filePath: change.filePath || 'unknown', error: error.message });
      }
    }

    res.json({
      success: true,
      data: {
        workspaceName,
        appliedChanges,
        errors,
        successCount: appliedChanges.length,
        errorCount: errors.length
      },
      message: `Successfully applied ${appliedChanges.length} changes${errors.length > 0 ? ` with ${errors.length} errors` : ''}`
    });

  } catch (error: any) {
    console.error('Error applying workspace changes:', error);
    res.status(500).json({
      success: false,
      message: 'Error applying workspace changes',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Helper function to apply diff operations to content
function applyDiffOperations(content: string, operations: any[]): string {
  let result = content;

  // Sort operations by position (descending) to avoid offset issues
  const sortedOps = operations.sort((a, b) => b.position - a.position);

  for (const op of sortedOps) {
    const { type, position, length, text } = op;

    switch (type) {
      case 'insert':
        result = result.slice(0, position) + text + result.slice(position);
        break;

      case 'delete':
        result = result.slice(0, position) + result.slice(position + length);
        break;

      case 'replace':
        result = result.slice(0, position) + text + result.slice(position + length);
        break;
    }
  }

  return result;
}

// Revert workspace from local filesystem
export const revertWorkspaceFromFilesystem = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const workspacePath = path.join(BASE_WORKSPACE_DIR, workspaceName);

    // Check if workspace exists
    const exists = await WorkspaceManager.ensureWorkspaceExists(workspacePath);
    if (!exists) {
      return res.status(404).json({
        success: false,
        message: 'Workspace not found on filesystem'
      });
    }

    // Read the current filesystem state
    const structure = await WorkspaceManager.readWorkspaceStructure(workspacePath);
    const files = flattenStructureToFiles(structure);

    res.json({
      success: true,
      data: {
        workspaceName,
        files,
        structure,
        count: Object.keys(files).length
      },
      message: `Successfully reverted workspace from filesystem with ${Object.keys(files).length} files`
    });

  } catch (error: any) {
    console.error('Error reverting workspace from filesystem:', error);
    res.status(500).json({
      success: false,
      message: 'Error reverting workspace from filesystem',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Extract files from mock data and create workspace
export const extractMockDataToWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    
    // Import CodeExtractor
    const { CodeExtractor } = await import('../utils/codeExtractor');
    
    // Read mock data from doc/llm_response.json
    const mockDataPath = path.join(process.cwd(), '..', 'doc', 'llm_response.json');
    
    try {
      const mockDataContent = await fs.readFile(mockDataPath, 'utf-8');
      const mockData = JSON.parse(mockDataContent);
      
      if (mockData.content && mockData.content[0]?.text) {
        const content = mockData.content[0].text;
        
        // Use CodeExtractor to extract files
        const extractedContent = CodeExtractor.extractFullContent(content);
        
        console.log('📁 CodeExtractor found:', {
          codeBlocksCount: extractedContent.codeBlocks.length,
          hasProjectStructure: !!extractedContent.projectStructure,
          hasDescription: !!extractedContent.description
        });
        
        // Convert code blocks to files
        const files: { [path: string]: string } = {};
        
        if (extractedContent.codeBlocks && extractedContent.codeBlocks.length > 0) {
          extractedContent.codeBlocks.forEach(block => {
            if (block.filename && block.content) {
              files[block.filename] = block.content;
            }
          });
        }
        
        // If no files extracted, create default structure
        if (Object.keys(files).length === 0) {
          files['README.md'] = '# Project\n\nThis is your generated project.';
          files['src/index.js'] = '// Your main application file\nconsole.log("Hello World!");';
        }
        
        console.log('📁 Extracted files:', Object.keys(files));
        
        // Save files to filesystem
        const workspacePath = path.join(BASE_WORKSPACE_DIR, workspaceName);
        await WorkspaceManager.createWorkspaceDir(workspaceName);
        
        const savedFiles: string[] = [];
        for (const [filePath, content] of Object.entries(files)) {
          await WorkspaceManager.writeFile(workspacePath, filePath, content);
          savedFiles.push(filePath);
        }
        
        res.json({
          success: true,
          data: {
            workspaceName,
            files,
            savedFiles,
            count: savedFiles.length,
            extractedContent: {
              description: extractedContent.description,
              deploymentInstructions: extractedContent.deploymentInstructions,
              additionalSections: extractedContent.additionalSections
            }
          },
          message: `Successfully extracted and saved ${savedFiles.length} files from mock data`
        });
        
      } else {
        throw new Error('Invalid mock data format');
      }
      
    } catch (fileError) {
      console.error('❌ Error reading mock data file:', fileError);
      
      // Fallback: create default files
      const defaultFiles = {
        'README.md': '# Project\n\nThis is your generated project.',
        'src/index.js': '// Your main application file\nconsole.log("Hello World!");'
      };
      
      const workspacePath = path.join(BASE_WORKSPACE_DIR, workspaceName);
      await WorkspaceManager.createWorkspaceDir(workspaceName);
      
      for (const [filePath, content] of Object.entries(defaultFiles)) {
        await WorkspaceManager.writeFile(workspacePath, filePath, content);
      }
      
      res.json({
        success: true,
        data: {
          workspaceName,
          files: defaultFiles,
          savedFiles: Object.keys(defaultFiles),
          count: Object.keys(defaultFiles).length
        },
        message: 'Created default workspace structure (mock data not available)'
      });
    }
    
  } catch (error: any) {
    console.error('Error extracting mock data to workspace:', error);
    res.status(500).json({
      success: false,
      message: 'Error extracting mock data to workspace',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Extract files from chat response and update workspace
export const extractChatResponseToWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const { chatResponse } = req.body;
    
    if (!chatResponse) {
      return res.status(400).json({
        success: false,
        message: 'Chat response content is required'
      });
    }
    
    // Import CodeExtractor
    const { CodeExtractor } = await import('../utils/codeExtractor');
    
    // Use CodeExtractor to extract files from chat response
    const extractedContent = CodeExtractor.extractFullContent(chatResponse);
    
    console.log('📁 CodeExtractor found in chat response:', {
      codeBlocksCount: extractedContent.codeBlocks.length,
      hasProjectStructure: !!extractedContent.projectStructure,
      hasDescription: !!extractedContent.description
    });
    
    // Convert code blocks to files
    const files: { [path: string]: string } = {};
    
    if (extractedContent.codeBlocks && extractedContent.codeBlocks.length > 0) {
      extractedContent.codeBlocks.forEach(block => {
        if (block.filename && block.content) {
          files[block.filename] = block.content;
        }
      });
    }
    
    if (Object.keys(files).length === 0) {
      return res.json({
        success: true,
        data: {
          workspaceName,
          files: {},
          savedFiles: [],
          count: 0
        },
        message: 'No files found in chat response'
      });
    }
    
    console.log('📁 Extracted files from chat response:', Object.keys(files));
    
    // Save files to filesystem (update existing or create new)
    const workspacePath = path.join(BASE_WORKSPACE_DIR, workspaceName);
    await WorkspaceManager.createWorkspaceDir(workspaceName);
    
    const savedFiles: string[] = [];
    for (const [filePath, content] of Object.entries(files)) {
      await WorkspaceManager.writeFile(workspacePath, filePath, content);
      savedFiles.push(filePath);
    }
    
    res.json({
      success: true,
      data: {
        workspaceName,
        files,
        savedFiles,
        count: savedFiles.length,
        extractedContent: {
          description: extractedContent.description,
          deploymentInstructions: extractedContent.deploymentInstructions,
          additionalSections: extractedContent.additionalSections
        }
      },
      message: `Successfully extracted and saved ${savedFiles.length} files from chat response`
    });
    
  } catch (error: any) {
    console.error('Error extracting chat response to workspace:', error);
    res.status(500).json({
      success: false,
      message: 'Error extracting chat response to workspace',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Helper function to flatten structure to files (moved from inside function)
const flattenStructureToFiles = (structure: any, basePath: string = ''): { [path: string]: string } => {
  const files: { [path: string]: string } = {};
  
  for (const [name, item] of Object.entries(structure)) {
    const currentPath = basePath ? `${basePath}/${name}` : name;
    
    if ((item as any).type === 'folder') {
      Object.assign(files, flattenStructureToFiles((item as any).children, currentPath));
    } else {
      files[currentPath] = (item as any).content || '';
    }
  }
  
  return files;
}; 