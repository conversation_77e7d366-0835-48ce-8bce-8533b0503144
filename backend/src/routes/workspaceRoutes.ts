import express from 'express';
import {
  getWorkspaceStructure,
  readWorkspace<PERSON><PERSON>,
  writeWorkspaceF<PERSON>,
  deleteWorkspaceFile,
  createWorkspace,
  listWorkspaces,
  saveWorkspaceToFilesystem,
  applyWorkspaceChanges,
  revertWorkspaceFromFilesystem,
  extractMockDataToWorkspace,
  extractChatResponseToWorkspace
} from '../controllers/workspaceController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// GET /api/workspace - List all workspaces
router.get('/', authenticateToken, listWorkspaces);

// POST /api/workspace - Create a new workspace
router.post('/', authenticateToken, createWorkspace);

// GET /api/workspace/:workspaceName/structure - Get workspace file structure
router.get('/:workspaceName/structure', authenticateToken, getWorkspaceStructure);

// POST /api/workspace/:workspaceName/files/read - Read a specific file
router.post('/:workspaceName/files/read', authenticateToken, readWorkspaceFile);

// POST /api/workspace/:workspaceName/files/write - Write/update a file
router.post('/:workspaceName/files/write', authenticateToken, writeWorkspaceFile);

// DELETE /api/workspace/:workspaceName/files - Delete a file
router.delete('/:workspaceName/files', authenticateToken, deleteWorkspaceFile);

// POST /api/workspace/:workspaceName/save - Save all workspace files to local filesystem
router.post('/:workspaceName/save', authenticateToken, saveWorkspaceToFilesystem);

// POST /api/workspace/:workspaceName/apply-changes - Apply file changes using diffs (optimized)
router.post('/:workspaceName/apply-changes', authenticateToken, applyWorkspaceChanges);

// POST /api/workspace/:workspaceName/revert - Revert workspace to last saved state
router.post('/:workspaceName/revert', authenticateToken, revertWorkspaceFromFilesystem);

// POST /api/workspace/:workspaceName/extract-mock - Extract files from mock data and create workspace
router.post('/:workspaceName/extract-mock', authenticateToken, extractMockDataToWorkspace);

// POST /api/workspace/:workspaceName/extract-chat - Extract files from chat response and update workspace
router.post('/:workspaceName/extract-chat', authenticateToken, extractChatResponseToWorkspace);

export default router; 