import express from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  createWorkspace,
  getWorkspaceStatus,
  stopWorkspace,
  removeWorkspace,
  createProject,
  getSupportedLanguages,
  getAvailableProjects,
  listUserWorkspaces,
  checkExistingWorkspace,
  getOrCreateWorkspace,
  uploadFileToWorkspace,
  getFileFromWorkspace,
  listWorkspaceFiles,
  modifyFileInWorkspace
} from '../controllers/sphereEngineController';

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Workspace management routes
router.post('/workspaces', createWorkspace);
router.post('/workspaces/get-or-create', getOrCreateWorkspace);
router.get('/workspaces', listUserWorkspaces);
router.get('/workspaces/check/:interviewUuid', checkExistingWorkspace);
router.get('/workspaces/:workspaceId', getWorkspaceStatus);
router.post('/workspaces/:workspaceId/stop', stopWorkspace);
router.delete('/workspaces/:workspaceId', removeWorkspace);

// Project management routes
router.post('/projects', createProject);

// Language support routes
router.get('/languages', getSupportedLanguages);

// Project management routes
router.get('/projects', getAvailableProjects);

// File management routes
router.post('/workspaces/:workspaceId/files', uploadFileToWorkspace);
router.get('/workspaces/:workspaceId/files', listWorkspaceFiles);
router.get('/workspaces/:workspaceId/files/:filepath(*)', getFileFromWorkspace);
router.put('/workspaces/:workspaceId/files', modifyFileInWorkspace);

export default router;